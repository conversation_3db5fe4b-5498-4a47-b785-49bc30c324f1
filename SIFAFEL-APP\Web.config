﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<!--
    Para obtener una descripción de los cambios de web.config, vea http://go.microsoft.com/fwlink/?LinkId=235367.

    Los siguientes atributos se pueden establecer en la etiqueta <httpRuntime>.
      <system.Web>
        <httpRuntime targetFramework="4.8.1" />
      </system.Web>
  -->
	<system.web>
		<compilation debug="true" targetFramework="4.8.1" />
		<httpRuntime targetFramework="4.7.2" />
	</system.web>
	<appSettings>
		<!--LOCAL-->
		<add key="url_redirect" value="http://localhost:54326/" />
		<!--<add key="url_cdn" value="http://localhost:57977/Express/" />-->
		<!---->
		<!--HOSTING DEV-->
		<!--<add key="url_redirect" value="http://grodriguezh1-002-site1.ltempurl.com/" />-->
		<add key="url_cdn" value="http://grodriguezh1-002-site3.ltempurl.com/CDN/" />
		<!---->
		<add key="EncryptionKey" value="eUEG5OLGuuWPAxE_SIFAFEL_202502.."/>
		<add key="EncryptionIV" value="p4LjDeToSiYs58RA"/>
		<!---->
		<!--RECAPTCHA-->
		<add key="recaptcha_validate" value="N" />
		<add key="recaptcha_key_public" value="6LdZtyQeAAAAAE8k8AYJDua56TuPHSZiwViTahim" />
		<add key="recaptcha_key_secret" value="6LdZtyQeAAAAAFWWDFjzD2nRIpzipXGuQNFJnWyN" />
		<!---->
		<!---->
		<!--API SIFAFEL LOCAL-->
		<!--<add key="api_url" value="http://localhost:57980/" />
		<add key="api_micro_service" value="http://localhost:55417/" />-->
		<!---->
		<!--API SIFAFEL HOSTING DEV-->
		<add key="api_url" value="http://grodriguezh1-002-site2.ltempurl.com/" />
		<add key="api_micro_service" value="http://grodriguezh1-002-site3.ltempurl.com" />
		<!---->
		<!--TOKEN-->
		<add key="API_GRANT_TYPE" value="password" />
		<add key="API_VALIDATION_TYPE" value="PASSWORD-SIFAFEL" />
		<add key="ORIGIN_APP" value="SIFAFEL-WEB" />
		<!---->
		<!--API AUTENTICACION-->
		<add key="api_sec_authentication_login" value="api/Security/Authentication/Login" />
		<add key="api_sec_authentication_user_info" value="api/Security/Session/GetUserInfo" />
		<add key="api_sec_authentication_user_menu" value="api/Security/Session/GetMenu" />
		<add key="api_sec_authentication_logout" value="api/Security/Session/LogOut" />
		<!---->
		<!--SEGURIDAD-->
		<!--API SEGURIDAD ACCIONES-->
		<add key="api_sec_accion_get_list" value="api/Security/Acciones/GetAll" />
		<add key="api_sec_accion_crear" value="api/Security/Acciones/Create" />
		<add key="api_sec_accion_editar" value="api/Security/Acciones/Edit" />
		<add key="api_sec_accion_eliminar" value="api/Security/Acciones/Delete" />
		<add key="api_sec_accion_getById" value="api/Security/Acciones/GetById" />
		<!--API SEGURIDAD TIPO USUARIO-->
		<add key="api_sec_tipo_usuario_get_list" value="api/Security/TipoUsuario/GetAll" />
		<add key="api_sec_tipo_usuario_crear" value="api/Security/TipoUsuario/Create" />
		<add key="api_sec_tipo_usuario_editar" value="api/Security/TipoUsuario/Edit" />
		<add key="api_sec_tipo_usuario_eliminar" value="api/Security/TipoUsuario/Delete" />
		<add key="api_sec_tipo_usuario_getById" value="api/Security/TipoUsuario/GetById" />
		<!---->
		<!--MODULOS-->
		<!--API MODULO PROVEEDORES-->
		<add key="api_mod_proveedor_get_list" value="api/Modulo/Proveedor/GetAll" />
		<add key="api_mod_proveedor_crear" value="api/Modulo/Proveedor/Create" />
		<add key="api_mod_proveedor_editar" value="api/Modulo/Proveedor/Edit" />
		<add key="api_mod_proveedor_eliminar" value="api/Modulo/Proveedor/Delete" />
		<add key="api_mod_proveedor_getById" value="api/Modulo/Proveedor/GetById" />
		<add key="api_mod_proveedor_getByNIT" value="api/Modulo/Proveedor/GetByNIT" />
		<!--API MODULO CLIENTES-->
		<add key="api_mod_cliente_get_list" value="api/Modulo/Cliente/GetAll" />
		<add key="api_mod_cliente_crear" value="api/Modulo/Cliente/Create" />
		<add key="api_mod_cliente_editar" value="api/Modulo/Cliente/Edit" />
		<add key="api_mod_cliente_eliminar" value="api/Modulo/Cliente/Delete" />
		<add key="api_mod_cliente_getById" value="api/Modulo/Cliente/GetById" />
		<add key="api_mod_cliente_getByNIT" value="api/Modulo/Cliente/GetByNIT" />
		<!--API MODULO CATEGORIA DE PRODUCTOS-->
		<add key="api_mod_categoria_producto_get_list_contribuyente" value="api/inventory/products/category/GetByContribuyente" />
		<add key="api_mod_sub_categoria_producto_get_list" value="api/inventory/products/subcategory/GetByIdCategoria" />
		<add key="api_mod_categoria_producto_get_list" value="api/Modulo/ProductoCategoria/GetAll" />
		<add key="api_mod_categoria_producto_crear" value="api/Modulo/ProductoCategoria/Create" />
		<add key="api_mod_categoria_producto_editar" value="api/Modulo/ProductoCategoria/Edit" />
		<add key="api_mod_categoria_producto_eliminar" value="api/Modulo/ProductoCategoria/Delete" />
		<add key="api_mod_categoria_producto_getById" value="api/Modulo/ProductoCategoria/GetById" />
		<!--API MODULO MARCA DE PRODUCTOS-->
		<add key="api_mod_marca_producto_get_list" value="api/Modulo/ProductoMarca/GetAll" />
		<add key="api_mod_marca_producto_crear" value="api/Modulo/ProductoMarca/Create" />
		<add key="api_mod_marca_producto_editar" value="api/Modulo/ProductoMarca/Edit" />
		<add key="api_mod_marca_producto_eliminar" value="api/Modulo/ProductoMarca/Delete" />
		<add key="api_mod_marca_producto_getById" value="api/Modulo/ProductoMarca/GetById" />
		<!--API MODULO MODELO DE PRODUCTOS-->
		<add key="api_mod_modelo_producto_get_list" value="api/Modulo/ProductoModelo/GetAll" />
		<add key="api_mod_modelo_producto_get_list_marca" value="api/Modulo/ProductoModelo/GetAllMarca" />
		<add key="api_mod_modelo_producto_crear" value="api/Modulo/ProductoModelo/Create" />
		<add key="api_mod_modelo_producto_editar" value="api/Modulo/ProductoModelo/Edit" />
		<add key="api_mod_modelo_producto_eliminar" value="api/Modulo/ProductoModelo/Delete" />
		<add key="api_mod_modelo_producto_getById" value="api/Modulo/ProductoModelo/GetById" />
		<!--API MODULO MONEDA-->
		<add key="api_mod_moneda_contribuyente_get_list" value="api/Modulo/MonedaContribuyente/GetAllContribuyente" />
		<!--API MODULO ANIO-->
		<add key="anios_permitidos" value="35" />
		<!--API MODULO DIR-->
		<add key="api_mod_dir_paises" value="api/Address/Pais/GetAll" />
		<!---->
		<!--API MODULO CAJA-->
		<add key="api_mod_caja_medios" value="api/Modulo/Caja/GetMedios" />
		<add key="api_mod_caja_apertura" value="api/Modulo/Caja/Aperturar" />
		<add key="api_mod_caja_cierre" value="api/Modulo/Caja/Cierre" />
		<add key="api_mod_cajas_abiertas" value="api/Modulo/Caja/Abiertas" />
		<add key="api_mod_cajas_operaciones" value="api/Modulo/Caja/HistorialOperaciones" />
		<add key="api_mod_cajas_transacciones" value="api/Modulo/Caja/HistorialTransacciones" />
		<!---->
		<!--API MODULO INVENTARIO-->
		<add key="api_inventory_get_products" value="api/inventory/products/GetProducts" />
		<add key="api_inventory_get_product_by_id" value="api/inventory/products/GetById" />
		<add key="api_inventory_get_product_existence" value="api/inventory/products/GetExistence" />
		<add key="api_inventory_get_product_create" value="api/inventory/products/CreateProduct" />
		<add key="api_mod_get_producto" value="api/inventory/products/GetProducto" />
		<add key="api_get_product_sucursal" value="api/inventory/products/GetProductoById" />
		<!---->
		<!--API MODULO VENTA-->
		<add key="api_mod_venta_guardar" value="api/venta/GuardarVenta" />
		<add key="api_mod_venta_listado" value="api/venta/Listado" />
		<add key="api_mod_venta_info" value="api/venta/Info" />
		<!--API MODULO COMPRA-->
		<add key="api_mod_guardar_compra" value="api/compra/GuardarCompra" />
		<add key="api_mod_compra_listado" value="api/compra/Listado" />
		<!--API CONFIGURATION MEDIO PAGo-->
		<add key="api_cof_medioPago_GetAll" value="api/Configuration/MedioPago/GetAll" />
		<add key="api_mod_medios_pago_sucursal" value="api/Modulo/MedioPago/Sucursal" />
		<add key="api_mod_cajas_medio_pago" value="api/Modulo/MedioPago/Transaccion" />
		<!--API MODULO BANCO-->
		<add key="api_mod_banco_get_list" value="api/Modulo/Banco/GetAll"/>
		<!---->
		<!--****************************************************-->
		<!--***************************REPORT ******************-->
		<add key="rpt_factura" value="api/rpt/facturacion/documento" />
	</appSettings>
	<system.codedom>
		<compilers>
			<compiler language="c#;cs;csharp" extension=".cs" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701;612;618" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
			<compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008,40000,40008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
		</compilers>
	</system.codedom>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
</configuration>